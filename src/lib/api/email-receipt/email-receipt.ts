import type { ReceiptEmailInfo } from "$lib/types";

export const emailReceipt = async (receiptEmailInfo: ReceiptEmailInfo) => {
  const response = await fetch("/api/email-receipt", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(receiptEmailInfo),
  });

  const result = await response.json();

  if (response.ok && result.success) {
    return { success: result.success };
  } else {
    return { success: false, error: result.error };
  }
};
