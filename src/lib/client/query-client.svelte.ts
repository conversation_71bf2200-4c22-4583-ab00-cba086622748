import { processPayment } from "$lib/api/process-payment";
import { emailReceipt } from "$lib/api/email-receipt";
import type { OrderInfo, NmiResponse, ReceiptEmailInfo } from "$lib/types";

type QueryClient = {
  processPayment: (args: {
    nmiToken: string;
    orderInfo: OrderInfo;
    deviceId: string;
  }) => Promise<{ paymentSuccessful: boolean; paymentResult: NmiResponse }>;
  emailReceipt: (receiptEmailInfo: ReceiptEmailInfo) => Promise<{ success: boolean }>;
};

const createClient = (): QueryClient => {
  return {
    processPayment,
    emailReceipt,
  };
};

// TODO Maybe use get and set context here
export const client = createClient();
