import type { RequestHand<PERSON> } from "@sveltejs/kit";
import { error, json } from "@sveltejs/kit";
import type { processPayment } from "$lib/api/process-payment";
import { processNmiPayment } from "$lib/api/nmi";
import { logger } from "$lib/logger/logger.svelte.ts";
import {
  OrderState,
  OrderStateTracker,
} from "$lib/services/OrderStateTracker.server.ts";
import { submitOrder } from "$lib/api/submit-order";
import { emailReceipt } from "$lib/api/email-receipt.server.ts";
import type { ReceiptEmailInfo } from "$lib/types";

export const POST: RequestHandler = async ({
  request,
  fetch,
  params,
  locals,
}) => {
  try {
    if (!params.deviceId) {
      error(400, "Missing device id");
    }

    const { nmiToken, orderInfo } = (await request.json()) as Parameters<
      typeof processPayment
    >[number];

    const parsedResult = await processNmiPayment({
      nmiToken,
      orderInfo,
      fetch,
      deviceId: params.deviceId,
    });

    await submitOrder({
      fetch,
      orderInfo,
      nmiDetails: parsedResult,
      deviceId: params.deviceId,
      bridgeApiToken: locals.bridgeApiToken || "",
    });

    const orderStateTracker = await OrderStateTracker.instance(orderInfo);
    await orderStateTracker.updateState(OrderState.COMPLETED);

    // Send email receipt after successful payment
    let emailSent = false;
    try {
      const receiptEmailInfo: ReceiptEmailInfo = {
        ...orderInfo,
        ...parsedResult,
      };
      await emailReceipt(receiptEmailInfo);
      emailSent = true;
      logger.info({ transactionId: parsedResult.transactionid }, "Email receipt sent successfully");
    } catch (emailError) {
      logger.error({ error: emailError, transactionId: parsedResult.transactionid }, "Failed to send email receipt");
      // Don't fail the entire payment process if email fails
    }

    return json({
      paymentSuccessful: true,
      paymentResult: parsedResult,
      emailSent,
    });
  } catch (error) {
    logger.error({ error }, "Error in process-payment");
    return json({
      paymentSuccessful: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
